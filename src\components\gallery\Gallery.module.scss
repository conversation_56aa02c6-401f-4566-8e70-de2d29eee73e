.masonryGrid {
    display: flex;
    margin-left: calc(-1 * var(--static-space-16));
    width: 100%;
}

.masonryGridColumn {
  padding-left: 1rem; // gutter size
  background-clip: padding-box;

  > div {
    margin-bottom: 1rem; // gutter size
  }
}

.gridItem {
    margin-bottom: var(--static-space-16);
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease; /* Transition για εμφάνιση/εξαφάνιση */
}

.modalOverlay.isOpen {
  opacity: 1;
  visibility: visible;
}

.modalContent {
  overflow: auto;
  background-color: transparent;
  transition: all 0.3s ease; /* Transition για το περιεχόμενο του modal */
}

.modalImage {
  display: block;
  max-width: 100vw; /* Η εικόνα δεν θα υπερβαίνει το 100% του πλάτους της οθόνης */
  max-height: 100vh; /* Η εικόνα δεν θα υπερβαίνει το 100% του ύψους της οθόνης */
  object-fit: contain;
}