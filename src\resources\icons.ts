import { IconType } from "react-icons";

import {
  HiArrowUpRight,
  HiOutlineLink,
  HiArrowTopRightOnSquare,
  HiEnvelope,
  HiCalendarDays,
  HiArrowRight,
  HiOutlineEye,
  HiOutlineEyeSlash,
  HiOutlineDocument,
  HiOutlineGlobeAsiaAustralia,
  HiOutlineRocketLaunch,
  HiMusicalNote,
} from "react-icons/hi2";

import {
  PiHouseDuotone,
  PiUserCircleDuotone,
  PiGridFourDuotone,
  PiBookBookmarkDuotone,
  PiImageDuotone,
} from "react-icons/pi";

import { FaDiscord, FaGithub, FaLinkedin, FaX, FaInstagram, FaPlay, FaPause, FaForward, FaBackward, FaVolumeHigh, FaVolumeLow, FaVolumeXmark } from "react-icons/fa6";

export const iconLibrary: Record<string, IconType> = {
  arrowUpRight: HiArrowUpRight,
  arrowRight: HiArrowRight,
  email: HiEnvel<PERSON>,
  globe: HiOutlineGlobeAsiaAustralia,
  person: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Duo<PERSON>,
  grid: Pi<PERSON>ridFourDuotone,
  book: <PERSON><PERSON><PERSON><PERSON><PERSON>markDuotone,
  openLink: HiOutlineLink,
  calendar: HiCalendarDays,
  home: PiHouseDuotone,
  gallery: PiImageDuotone,
  discord: FaDiscord,
  eye: HiOutlineEye,
  eyeOff: HiOutlineEyeSlash,
  github: FaGithub,
  linkedin: FaLinkedin,
  x: FaX,
  instagram: FaInstagram,
  arrowUpRightFromSquare: HiArrowTopRightOnSquare,
  document: HiOutlineDocument,
  rocket: HiOutlineRocketLaunch,
  play: FaPlay,
  pause: FaPause,
  forward: FaForward,
  backward: FaBackward,
  music: HiMusicalNote,
  volumeUp: FaVolumeHigh,
  volumeDown: FaVolumeLow,
  volumeOff: FaVolumeXmark
};

export type IconLibrary = typeof iconLibrary;
export type IconName = keyof IconLibrary;